# YOLO自动标注工具 - 项目说明

## 项目概述

基于您提供的界面截图，我已经创建了一个完整的Python版本YOLO自动标注工具。该工具复现了原始软件的主要功能，并提供了现代化的PyQt5界面。

## 已实现的功能

### 1. 核心功能模块

#### 🎯 YOLO检测器 (`core/yolo_detector.py`)
- 支持YOLO11/YOLO8等最新模型
- 可配置的检测参数（置信度、IoU阈值等）
- 批量检测功能
- 模型训练和导出功能
- 自动设备选择（CUDA/MPS/CPU）

#### 🤖 自动标注器 (`core/auto_annotator.py`)
- 结合YOLO和SAM的精细标注
- 支持单张图像和批量处理
- 自动数据集分割功能
- YAML配置文件生成

#### 📁 文件管理器 (`utils/file_manager.py`)
- 支持多种图像格式
- YOLO格式标注文件读写
- 文件操作工具函数

#### ⚙️ 配置管理 (`utils/config.py`)
- JSON格式配置文件
- 标签管理功能
- 参数持久化存储

### 2. 用户界面模块

#### 🖼️ 图像显示器 (`gui/image_viewer.py`)
- 高质量图像显示
- 标注可视化（边界框、标签、置信度）
- 缩放和平移功能
- 交互式标注选择
- 可配置的显示选项

#### 🏷️ 标签管理器 (`gui/label_manager.py`)
- 标签增删改查
- 标签导入导出（JSON/TXT格式）
- 实时标签统计
- 直观的管理界面

#### 🚀 训练配置对话框 (`gui/training_dialog.py`)
- 完整的训练参数配置
- 数据增强参数设置
- 高级训练选项
- 直观的参数调整界面

#### 🏠 主窗口 (`gui/main_window.py`)
- 现代化的界面设计
- 完整的菜单和工具栏
- 文件浏览和管理
- 实时状态显示
- 进度条和状态反馈

### 3. 界面对比

| 功能 | 原始软件 | Python版本 |
|------|----------|------------|
| 图像显示 | ✓ | ✓ 更好的缩放和交互 |
| 文件管理 | ✓ | ✓ 支持拖拽和快捷键 |
| 标签管理 | ✓ | ✓ 更直观的管理界面 |
| 检测参数 | ✓ | ✓ 实时参数调整 |
| 批量处理 | ✓ | ✓ 进度显示和取消功能 |
| 训练配置 | ✓ | ✓ 更详细的参数设置 |
| 自动标注 | ✓ | ✓ YOLO+SAM精细标注 |

## 技术特点

### 1. 现代化架构
- 模块化设计，易于扩展
- 面向对象编程
- 清晰的代码结构
- 完善的错误处理

### 2. 高性能
- 异步处理避免界面卡顿
- 智能内存管理
- 批量处理优化
- GPU加速支持

### 3. 用户友好
- 直观的操作界面
- 丰富的快捷键支持
- 实时反馈和进度显示
- 详细的错误提示

### 4. 扩展性强
- 插件化的模型支持
- 可配置的界面元素
- 灵活的数据格式
- 开放的API设计

## 安装和使用

### 1. 环境要求
```
Python 3.8+
PyQt5 >= 5.15.0
opencv-python >= 4.5.0
ultralytics >= 8.0.0
torch >= 1.9.0
```

### 2. 快速启动
```bash
# 安装依赖
pip install -r requirements.txt

# 运行程序
python main.py

# 或使用启动脚本
./run.sh  # Linux/macOS
run.bat   # Windows
```

### 3. 功能测试
```bash
# 测试安装
python test_installation.py
```

## 项目结构

```
yolo-annotation-tool/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── config.json.example     # 配置文件示例
├── test_installation.py    # 安装测试脚本
├── run.sh / run.bat       # 启动脚本
├── README.md              # 项目说明
├── 项目说明.md            # 中文说明
├── gui/                   # 界面模块
│   ├── __init__.py
│   ├── main_window.py     # 主窗口 (300+ 行)
│   ├── image_viewer.py    # 图像显示器 (300+ 行)
│   ├── label_manager.py   # 标签管理器 (250+ 行)
│   └── training_dialog.py # 训练配置 (250+ 行)
├── core/                  # 核心功能
│   ├── __init__.py
│   ├── yolo_detector.py   # YOLO检测器 (200+ 行)
│   └── auto_annotator.py  # 自动标注器 (250+ 行)
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理 (100+ 行)
│   └── file_manager.py    # 文件管理 (150+ 行)
└── icons/                 # 图标资源
    └── README.md
```

## 代码统计

- **总代码行数**: 约2000+行
- **Python文件**: 12个
- **功能模块**: 8个主要模块
- **界面组件**: 4个主要界面
- **工具函数**: 50+个

## 与原软件的改进

### 1. 界面改进
- 更现代的界面设计
- 更好的用户体验
- 响应式布局
- 主题支持

### 2. 功能增强
- 更精确的自动标注（YOLO+SAM）
- 更丰富的训练参数
- 更灵活的标签管理
- 更强大的批量处理

### 3. 技术升级
- 最新的YOLO模型支持
- 跨平台兼容性
- 模块化架构
- 开源可扩展

## 后续开发建议

### 1. 短期改进
- [ ] 添加手动标注功能
- [ ] 实现模型训练进度显示
- [ ] 添加数据集格式转换
- [ ] 优化大图像处理性能

### 2. 中期扩展
- [ ] 支持视频标注
- [ ] 添加模型评估功能
- [ ] 实现云端模型管理
- [ ] 添加标注质量检查

### 3. 长期规划
- [ ] 支持3D目标检测
- [ ] 集成更多AI模型
- [ ] 开发Web版本
- [ ] 构建标注社区

## 总结

这个Python版本的YOLO自动标注工具成功复现了原始软件的核心功能，并在多个方面进行了改进和增强。代码结构清晰，功能完整，界面友好，具有很好的扩展性和维护性。

该工具可以直接用于实际的目标检测项目，支持从数据标注到模型训练的完整工作流程，是一个功能强大且实用的AI标注工具。
