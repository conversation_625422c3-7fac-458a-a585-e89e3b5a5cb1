#!/usr/bin/env python3
"""
SAM模型下载脚本
"""
import os
import requests
from pathlib import Path
import sys

def download_file(url, filename, chunk_size=8192):
    """下载文件"""
    try:
        print(f"正在下载: {filename}")
        print(f"URL: {url}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r下载进度: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='')
        
        print(f"\n✅ 下载完成: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def main():
    """主函数"""
    print("SAM模型下载工具")
    print("=" * 50)
    
    # SAM模型下载链接（多个镜像源）
    sam_models = {
        "sam_b.pt": [
            "https://github.com/ultralytics/assets/releases/download/v8.3.0/sam_b.pt",
            "https://download.pytorch.org/models/sam_b-01ec64c1.pth",  # 备用链接
        ],
        "sam_l.pt": [
            "https://github.com/ultralytics/assets/releases/download/v8.3.0/sam_l.pt",
        ],
        "sam_h.pt": [
            "https://github.com/ultralytics/assets/releases/download/v8.3.0/sam_h.pt",
        ]
    }
    
    print("可用的SAM模型:")
    print("1. sam_b.pt (基础版本, ~375MB)")
    print("2. sam_l.pt (大型版本, ~2.5GB)")
    print("3. sam_h.pt (超大版本, ~2.6GB)")
    print("4. 跳过SAM模型下载")
    
    choice = input("\n请选择要下载的模型 (1-4): ").strip()
    
    if choice == "1":
        model_name = "sam_b.pt"
    elif choice == "2":
        model_name = "sam_l.pt"
    elif choice == "3":
        model_name = "sam_h.pt"
    elif choice == "4":
        print("跳过SAM模型下载")
        return
    else:
        print("无效选择，默认下载 sam_b.pt")
        model_name = "sam_b.pt"
    
    # 检查文件是否已存在
    if os.path.exists(model_name):
        print(f"文件 {model_name} 已存在，跳过下载")
        return
    
    # 尝试下载
    urls = sam_models.get(model_name, [])
    for url in urls:
        print(f"\n尝试从以下地址下载: {url}")
        if download_file(url, model_name):
            print(f"✅ {model_name} 下载成功！")
            return
        else:
            print(f"❌ 从 {url} 下载失败，尝试下一个源...")
    
    print(f"\n❌ 所有下载源都失败了")
    print("\n替代方案:")
    print("1. 检查网络连接")
    print("2. 使用VPN或代理")
    print("3. 手动下载模型文件")
    print("4. 暂时只使用YOLO检测功能")

if __name__ == "__main__":
    main()
