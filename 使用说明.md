# YOLO自动标注工具 - 使用说明

## 🎉 恭喜！安装成功

您的YOLO自动标注工具已经成功安装并可以运行了！

## 📋 安装状态

✅ **所有依赖包已安装**
- PyQt5 - GUI界面框架
- OpenCV - 图像处理
- PyTorch - 深度学习框架 (支持CUDA)
- Ultralytics - YOLO模型
- 其他必要依赖包

✅ **所有项目模块已加载**
- 核心检测模块
- 自动标注模块
- 图像显示组件
- 标签管理系统
- 训练配置界面

## 🚀 启动程序

### 方法1: 直接运行
```bash
python main.py
```

### 方法2: 使用启动脚本
**Windows:**
```bash
start_app.bat
```

**Linux/macOS:**
```bash
chmod +x start_app.sh
./start_app.sh
```

### 方法3: 完整环境设置（首次使用推荐）
**Windows:**
```bash
simple_setup.bat
```

**Linux/macOS:**
```bash
chmod +x setup_and_run.sh
./setup_and_run.sh
```

## 🖥️ 界面介绍

程序启动后，您将看到现代化的PyQt5界面，包含：

### 左侧面板
- **文件列表**: 浏览和管理图像文件
- **标签管理**: 添加、编辑、删除标签类别
- **检测参数**: 调整置信度、IoU阈值等
- **操作按钮**: 检测、自动标注、批量处理

### 右侧面板
- **图像显示区**: 高质量图像显示和标注可视化
- **缩放控制**: 放大、缩小、重置视图
- **显示选项**: 控制标签和置信度显示

### 菜单栏
- **文件**: 打开文件/文件夹、保存标注
- **编辑**: 清除标注、编辑功能
- **检测**: 各种检测和标注功能
- **模型**: 加载模型、训练配置
- **工具**: 数据集转换等工具
- **帮助**: 关于信息

## 📖 基本使用流程

### 1. 打开图像
- 点击"打开文件"选择单个图像
- 点击"打开文件夹"批量加载图像
- 使用文件列表浏览图像

### 2. 调整参数
- **置信度**: 检测结果的可信度阈值 (0.01-1.0)
- **IoU阈值**: 重叠检测框的合并阈值 (0.01-1.0)
- **图像尺寸**: 模型输入尺寸 (320-1280)
- **最大检测数**: 单张图像最大检测目标数

### 3. 执行检测
- **检测当前图像**: 对当前显示的图像进行检测
- **自动标注**: 使用YOLO+SAM进行精细标注
- **批量标注**: 对整个文件夹进行批量处理

### 4. 管理标注
- **查看结果**: 在图像上查看检测框和标签
- **保存标注**: 保存为YOLO格式的txt文件
- **清除标注**: 清除当前图像的所有标注

### 5. 标签管理
- **添加标签**: 创建新的目标类别
- **编辑标签**: 修改现有标签名称
- **导入/导出**: 支持JSON和TXT格式

## ⚙️ 高级功能

### 模型训练
1. 点击"模型" → "训练模型"
2. 配置数据集路径和训练参数
3. 设置数据增强选项
4. 开始训练过程

### 批量处理
1. 点击"批量标注"
2. 选择输入目录（包含图像）
3. 选择输出目录（保存标注）
4. 等待处理完成

### 数据集转换
- 支持多种标注格式转换
- 自动数据集分割（训练/验证/测试）
- 生成YAML配置文件

## 🔧 配置文件

程序会自动创建 `config.json` 配置文件，包含：
- 上次使用的目录路径
- 检测参数设置
- 标签配置信息
- 模型路径等

## 📁 输出文件

### 标注文件格式 (YOLO)
```
class_id x_center y_center width height [confidence]
```
- 坐标为相对值 (0-1)
- 保存在对应的 `labels` 目录

### 目录结构
```
your_dataset/
├── images/
│   ├── image1.jpg
│   └── image2.jpg
└── labels/
    ├── image1.txt
    └── image2.txt
```

## 🐛 常见问题

### Q: 程序启动失败
A: 运行 `python test_installation.py` 检查依赖

### Q: 检测结果不准确
A: 调整置信度阈值或使用更大的模型

### Q: 内存不足
A: 减小图像尺寸参数或批次大小

### Q: CUDA不可用
A: 程序会自动使用CPU，性能稍慢但功能完整

## 📞 技术支持

如遇问题，请：
1. 查看控制台错误信息
2. 运行安装测试脚本
3. 检查配置文件设置
4. 参考项目文档

## 🎯 下一步

现在您可以：
1. 🖼️ 加载您的图像数据
2. 🎯 开始目标检测
3. 🏷️ 管理标签类别
4. 🚀 训练自定义模型
5. 📊 批量处理数据

祝您使用愉快！🎉
