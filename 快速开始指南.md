# 🚀 YOLO自动标注工具 - 快速开始指南

## 🎉 恭喜！程序已成功运行

从您的截图可以看到，YOLO自动标注工具已经完美启动！界面显示正常，所有基础功能都可以使用。

## 📋 当前功能状态

### ✅ 完全可用的功能
- **YOLO目标检测** - 核心功能，完全正常
- **图像浏览和管理** - 支持多种图像格式
- **标签管理系统** - 添加、编辑、删除标签
- **批量处理** - 大规模图像自动标注
- **参数调整** - 置信度、IoU阈值等
- **结果保存** - YOLO格式标注文件

### ⚠️ 需要额外设置的功能
- **SAM精细分割** - 需要下载SAM模型（可选）

## 🎯 立即开始使用

### 第1步: 加载图像
1. 点击界面左上角的 **"打开文件夹"** 按钮
2. 选择包含图像的文件夹
3. 图像列表将显示在左侧面板中

### 第2步: 调整检测参数
在左侧 **"检测参数"** 区域：
- **置信度**: 0.25 (默认值，可调整)
- **IoU阈值**: 0.45 (默认值，可调整)
- **图像尺寸**: 640 (默认值，可调整)
- **最大检测数**: 300 (默认值，可调整)

### 第3步: 执行检测
1. 在文件列表中选择一张图像
2. 点击 **"检测当前图像"** 按钮
3. 等待检测完成，结果将显示在右侧图像区域

### 第4步: 保存结果
1. 检测完成后，点击 **"保存标注"** 按钮
2. 标注文件将自动保存为YOLO格式

## 🔧 功能详解

### 标签管理
当前已有标签: `0: renwu`
- **添加**: 点击"添加"按钮创建新标签
- **删除**: 选择标签后点击"删除"
- **编辑**: 选择标签后点击"编辑"修改名称

### 批量处理
1. 点击 **"批量标注"** 按钮
2. 选择输入文件夹（包含图像）
3. 选择输出文件夹（保存标注）
4. 等待批量处理完成

### 显示选项
右上角的复选框：
- **显示标签** ✅ - 显示检测目标的类别名称
- **显示置信度** ✅ - 显示检测的可信度分数

## 📁 输出文件说明

### 标注文件格式
每个图像对应一个 `.txt` 文件，格式为：
```
class_id x_center y_center width height confidence
```

### 示例
```
0 0.5 0.3 0.2 0.4 0.85
```
表示：
- 类别ID: 0 (renwu)
- 中心点: (0.5, 0.3) - 相对坐标
- 尺寸: 宽0.2, 高0.4 - 相对尺寸
- 置信度: 0.85

## 🛠️ 关于SAM模型

### 当前状态
SAM模型下载失败，但这**不影响核心功能**：
- YOLO检测功能完全正常
- 自动标注功能可用（基于YOLO）
- 所有其他功能正常

### 如果需要SAM精细分割
SAM模型可以提供更精确的目标边界，如需使用：

1. **手动下载**:
   ```bash
   python download_sam_model.py
   ```

2. **或者使用代理/VPN**后重启程序

3. **或者暂时跳过**，继续使用YOLO检测

## 💡 使用技巧

### 提高检测精度
- 降低置信度阈值 (如0.15) 检测更多目标
- 提高置信度阈值 (如0.5) 只保留高质量检测

### 处理不同尺寸图像
- 小图像: 使用较小的图像尺寸 (320-480)
- 大图像: 使用较大的图像尺寸 (640-1280)

### 批量处理建议
- 先用单张图像测试参数
- 确认效果满意后再批量处理
- 大批量处理时建议分批进行

## 🎯 常用工作流程

### 场景1: 单张图像标注
1. 打开文件 → 2. 调整参数 → 3. 检测 → 4. 保存

### 场景2: 批量数据处理
1. 打开文件夹 → 2. 测试参数 → 3. 批量标注 → 4. 检查结果

### 场景3: 自定义标签
1. 标签管理 → 2. 添加标签 → 3. 检测标注 → 4. 保存导出

## 🚀 现在就开始吧！

您的YOLO自动标注工具已经完全准备就绪！
1. 准备一些图像文件
2. 按照上述步骤操作
3. 开始您的目标检测和标注工作

如有任何问题，请参考详细的 `使用说明.md` 文件。

祝您使用愉快！🎉
