@echo off
chcp 65001 >nul
echo ========================================
echo YOLO自动标注工具 - 环境设置和运行脚本
echo ========================================
echo.

REM 检查Python是否安装
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
python --version
echo ✅ Python环境检查通过
echo.

REM 创建虚拟环境
echo [2/6] 创建虚拟环境...
if exist "yolo_env" (
    echo ℹ️  虚拟环境已存在，跳过创建
) else (
    echo 正在创建虚拟环境 yolo_env...
    python -m venv yolo_env
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)
echo.

REM 激活虚拟环境
echo [3/6] 激活虚拟环境...
call yolo_env\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)
echo ✅ 虚拟环境已激活
echo.

REM 升级pip
echo [4/6] 升级pip...
python -m pip install --upgrade pip
echo ✅ pip升级完成
echo.

REM 安装依赖包
echo [5/6] 安装依赖包...
echo 正在安装依赖包，这可能需要几分钟时间...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    echo.
    echo 尝试使用国内镜像源安装...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if errorlevel 1 (
        echo ❌ 依赖包安装仍然失败，请检查网络连接
        pause
        exit /b 1
    )
)
echo ✅ 依赖包安装完成
echo.

REM 运行测试
echo [6/6] 运行安装测试...
python test_installation.py
if errorlevel 1 (
    echo ⚠️  安装测试发现问题，但仍将尝试启动程序
    echo.
)

echo ========================================
echo 🚀 启动YOLO自动标注工具
echo ========================================
echo.

REM 运行主程序
python main.py

echo.
echo 程序已退出
pause
