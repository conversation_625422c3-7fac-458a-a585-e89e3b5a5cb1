#!/bin/bash

echo "========================================"
echo "YOLO自动标注工具 - 环境设置和运行脚本"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Python是否安装
echo -e "${BLUE}[1/6] 检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Python3，请先安装Python 3.8+${NC}"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
    echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

python3 --version
echo -e "${GREEN}✅ Python环境检查通过${NC}"
echo

# 创建虚拟环境
echo -e "${BLUE}[2/6] 创建虚拟环境...${NC}"
if [ -d "yolo_env" ]; then
    echo -e "${YELLOW}ℹ️  虚拟环境已存在，跳过创建${NC}"
else
    echo "正在创建虚拟环境 yolo_env..."
    python3 -m venv yolo_env
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 虚拟环境创建失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 虚拟环境创建成功${NC}"
fi
echo

# 激活虚拟环境
echo -e "${BLUE}[3/6] 激活虚拟环境...${NC}"
source yolo_env/bin/activate
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 虚拟环境激活失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 虚拟环境已激活${NC}"
echo

# 升级pip
echo -e "${BLUE}[4/6] 升级pip...${NC}"
python -m pip install --upgrade pip
echo -e "${GREEN}✅ pip升级完成${NC}"
echo

# 安装依赖包
echo -e "${BLUE}[5/6] 安装依赖包...${NC}"
echo "正在安装依赖包，这可能需要几分钟时间..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 依赖包安装失败${NC}"
    echo
    echo "尝试使用国内镜像源安装..."
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖包安装仍然失败，请检查网络连接${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}✅ 依赖包安装完成${NC}"
echo

# 运行测试
echo -e "${BLUE}[6/6] 运行安装测试...${NC}"
python test_installation.py
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️  安装测试发现问题，但仍将尝试启动程序${NC}"
    echo
fi

echo "========================================"
echo -e "${GREEN}🚀 启动YOLO自动标注工具${NC}"
echo "========================================"
echo

# 运行主程序
python main.py

echo
echo "程序已退出"
read -p "按回车键继续..."
