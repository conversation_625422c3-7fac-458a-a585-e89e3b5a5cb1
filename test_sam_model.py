#!/usr/bin/env python3
"""
测试SAM模型加载
"""
import os
import sys
from pathlib import Path

def test_sam_model_files():
    """测试SAM模型文件"""
    print("SAM模型文件检查")
    print("=" * 50)
    
    # 检查可能的SAM模型路径
    sam_paths = [
        "_internal/sampro/checkpoints/sam2.1_hiera_large.pt",
        "_internal/sam2/sam2_hiera_l.yaml",
        "_internal/sam2/sam2_hiera_b+.yaml",
        "_internal/sam2/sam2_hiera_s.yaml",
        "_internal/sam2/sam2_hiera_t.yaml",
        "sam_b.pt",
        "sam_l.pt",
        "sam_h.pt",
    ]
    
    found_models = []
    
    for path in sam_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path)
            size_mb = file_size / (1024 * 1024)
            print(f"✅ 找到: {path} ({size_mb:.1f} MB)")
            found_models.append(path)
        else:
            print(f"❌ 未找到: {path}")
    
    print(f"\n总共找到 {len(found_models)} 个SAM模型文件")
    return found_models

def test_sam_loading():
    """测试SAM模型加载"""
    print("\nSAM模型加载测试")
    print("=" * 50)
    
    try:
        from ultralytics import SAM
        print("✅ Ultralytics SAM模块可用")
    except ImportError as e:
        print(f"❌ Ultralytics SAM模块不可用: {e}")
        return False
    
    # 测试加载内置模型
    model_path = "_internal/sampro/checkpoints/sam2.1_hiera_large.pt"
    if os.path.exists(model_path):
        try:
            print(f"正在测试加载: {model_path}")
            sam_model = SAM(model_path)
            print(f"✅ 成功加载SAM模型: {model_path}")
            return True
        except Exception as e:
            print(f"❌ 加载SAM模型失败: {e}")
            return False
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        return False

def test_auto_annotator():
    """测试自动标注器"""
    print("\n自动标注器测试")
    print("=" * 50)
    
    try:
        from core.auto_annotator import AutoAnnotator
        
        print("正在初始化自动标注器...")
        annotator = AutoAnnotator()
        
        if annotator.sam_model is not None:
            print("✅ 自动标注器初始化成功，SAM模型已加载")
            return True
        else:
            print("⚠️ 自动标注器初始化成功，但SAM模型未加载")
            print("   YOLO检测功能仍然可用")
            return True
            
    except Exception as e:
        print(f"❌ 自动标注器初始化失败: {e}")
        return False

def test_sam2_direct():
    """直接测试SAM2模块"""
    print("\n直接SAM2模块测试")
    print("=" * 50)
    
    # 添加SAM2路径到Python路径
    sam2_path = "_internal/sam2"
    if os.path.exists(sam2_path):
        sys.path.insert(0, sam2_path)
        
        try:
            from sam2.build_sam import build_sam2
            from sam2.sam2_image_predictor import SAM2ImagePredictor
            print("✅ SAM2模块导入成功")
            
            # 尝试构建模型
            model_path = "_internal/sampro/checkpoints/sam2.1_hiera_large.pt"
            config_path = "_internal/sam2/sam2_hiera_l.yaml"
            
            if os.path.exists(model_path) and os.path.exists(config_path):
                try:
                    sam2_model = build_sam2(config_path, model_path)
                    predictor = SAM2ImagePredictor(sam2_model)
                    print("✅ SAM2模型构建成功")
                    return True
                except Exception as e:
                    print(f"❌ SAM2模型构建失败: {e}")
                    return False
            else:
                print("❌ SAM2配置文件或权重文件不存在")
                return False
                
        except ImportError as e:
            print(f"❌ SAM2模块导入失败: {e}")
            return False
    else:
        print("❌ SAM2目录不存在")
        return False

def main():
    """主函数"""
    print("YOLO自动标注工具 - SAM模型测试")
    print("=" * 60)
    
    # 测试文件存在性
    found_models = test_sam_model_files()
    
    # 测试SAM加载
    sam_loaded = test_sam_loading()
    
    # 测试自动标注器
    annotator_ok = test_auto_annotator()
    
    # 测试SAM2直接加载
    sam2_ok = test_sam2_direct()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"- 找到模型文件: {len(found_models)} 个")
    print(f"- Ultralytics SAM加载: {'✅ 成功' if sam_loaded else '❌ 失败'}")
    print(f"- 自动标注器: {'✅ 正常' if annotator_ok else '❌ 异常'}")
    print(f"- SAM2直接加载: {'✅ 成功' if sam2_ok else '❌ 失败'}")
    
    if found_models and (sam_loaded or sam2_ok):
        print("\n🎉 SAM模型可用！精细分割功能已启用")
        return 0
    elif annotator_ok:
        print("\n⚠️ SAM模型不可用，但YOLO检测功能正常")
        return 0
    else:
        print("\n❌ 存在问题，请检查安装")
        return 1

if __name__ == "__main__":
    sys.exit(main())
