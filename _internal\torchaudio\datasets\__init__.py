from .cmuarctic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .cmudict import <PERSON><PERSON><PERSON><PERSON>
from .commonvoice import COMM<PERSON><PERSON><PERSON><PERSON>
from .dr_vctk import DR_VCTK
from .fluentcommands import FluentSpeechCommands
from .gtzan import GT<PERSON><PERSON>
from .iemocap import IEMOCAP
from .librilight_limited import <PERSON>bri<PERSON>ightLimited
from .librimix import LibriMix
from .librispeech import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .librispeech_biasing import LibriSpeechBiasing
from .libritts import LIBRITTS
from .ljspeech import LJSPEECH
from .musdb_hq import MUSDB_HQ
from .quesst14 import QUESST14
from .snips import Snips
from .speechcommands import SPEECHCOMMANDS
from .tedlium import TEDLIUM
from .vctk import VCTK_092
from .voxceleb1 import VoxCeleb1Identification, VoxCeleb1Verification
from .yesno import YESNO


__all__ = [
    "COMMONVOICE",
    "LIBRISPEECH",
    "LibriSpeechBiasing",
    "LibriLightLimited",
    "SPEECHCOMMANDS",
    "VCTK_092",
    "DR_VCTK",
    "YESN<PERSON>",
    "LJ<PERSON>EECH",
    "GTZ<PERSON>",
    "CMUARCT<PERSON>",
    "CMUDict",
    "LibriMix",
    "LIBRITTS",
    "TEDLIUM",
    "QUESST14",
    "MUSDB_HQ",
    "FluentSpeechCommands",
    "VoxCeleb1Identification",
    "VoxCeleb1Verification",
    "IEMOCAP",
    "Snips",
]
