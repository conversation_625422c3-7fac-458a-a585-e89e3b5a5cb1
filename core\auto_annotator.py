"""
自动标注模块
"""
import os
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional
from .yolo_detector import YOLODetector
from utils.file_manager import FileManager

try:
    from ultralytics import SAM
except ImportError:
    print("警告: SAM模型不可用")
    SAM = None

class AutoAnnotator:
    """自动标注器"""

    def __init__(self, det_model_path: str = "yolo11n.pt", sam_model_path: str = None):
        self.yolo_detector = YOLODetector(det_model_path)
        self.sam_model = None

        # 优先使用项目内置的SAM模型
        if sam_model_path is None:
            # 检查标准SAM模型（Ultralytics支持的）
            standard_sam_models = [
                "sam2.1_l.pt",  # 对应内置的large模型
                "sam2_l.pt",
                "sam_l.pt",
                "sam_b.pt",
                "mobile_sam.pt"
            ]

            # 首先尝试标准模型名称
            for model_name in standard_sam_models:
                if os.path.exists(model_name):
                    sam_model_path = model_name
                    print(f"发现SAM模型: {model_name}")
                    break

            # 如果没有找到，使用默认值让Ultralytics自动下载
            if sam_model_path is None:
                sam_model_path = "sam2.1_l.pt"  # 使用支持的模型名称

        self.sam_model_path = sam_model_path
        self.load_sam_model()
    
    def load_sam_model(self):
        """加载SAM模型"""
        if SAM is None:
            print("SAM模型不可用，将只使用YOLO检测")
            return False

        try:
            # 检查文件是否存在
            if not os.path.exists(self.sam_model_path):
                print(f"SAM模型文件不存在: {self.sam_model_path}")
                return False

            # 根据文件扩展名选择加载方式
            if self.sam_model_path.endswith('.pt'):
                # 尝试使用ultralytics SAM
                self.sam_model = SAM(self.sam_model_path)
                print(f"成功加载SAM模型: {self.sam_model_path}")
                return True
            elif self.sam_model_path.endswith('.yaml'):
                # 配置文件，需要对应的权重文件
                print(f"检测到YAML配置文件: {self.sam_model_path}")
                print("需要对应的权重文件才能加载")
                return False
            else:
                print(f"不支持的SAM模型格式: {self.sam_model_path}")
                return False

        except Exception as e:
            print(f"加载SAM模型失败: {e}")
            print("提示: SAM模型用于精细分割，不影响基础YOLO检测功能")
            print("将继续使用YOLO检测功能")
            return False
    
    def auto_annotate_image(self, image_path: str, output_dir: Optional[str] = None,
                           conf: float = 0.25, iou: float = 0.45, 
                           classes: Optional[List[int]] = None) -> List[Dict]:
        """
        自动标注单张图像
        
        Args:
            image_path: 图像路径
            output_dir: 输出目录
            conf: 置信度阈值
            iou: IoU阈值
            classes: 指定检测的类别
        
        Returns:
            标注结果列表
        """
        # 使用YOLO检测目标
        detections = self.yolo_detector.detect(
            image_path, conf=conf, iou=iou, classes=classes
        )
        
        if not detections:
            return []
        
        # 如果有SAM模型，使用SAM进行精细分割
        if self.sam_model is not None:
            print("使用SAM模型进行精细分割...")
            detections = self._refine_with_sam(image_path, detections)
        else:
            print("使用YOLO检测结果（未启用SAM精细分割）")

        # 保存标注结果
        if output_dir:
            FileManager.save_yolo_annotation(image_path, detections, output_dir)
        
        return detections
    
    def _refine_with_sam(self, image_path: str, detections: List[Dict]) -> List[Dict]:
        """使用SAM精细化检测结果"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            img_height, img_width = image.shape[:2]
            
            # 准备SAM输入
            boxes = []
            for det in detections:
                # 转换为绝对坐标
                x_center = det['x_center'] * img_width
                y_center = det['y_center'] * img_height
                width = det['width'] * img_width
                height = det['height'] * img_height
                
                x1 = x_center - width / 2
                y1 = y_center - height / 2
                x2 = x_center + width / 2
                y2 = y_center + height / 2
                
                boxes.append([x1, y1, x2, y2])
            
            # 使用SAM进行分割
            results = self.sam_model(image_path, bboxes=boxes)
            
            # 更新检测结果
            if results and len(results) > 0:
                result = results[0]
                if hasattr(result, 'masks') and result.masks is not None:
                    masks = result.masks.data.cpu().numpy()
                    
                    for i, (det, mask) in enumerate(zip(detections, masks)):
                        # 从mask计算更精确的边界框
                        mask_uint8 = (mask * 255).astype(np.uint8)
                        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                        
                        if contours:
                            # 获取最大轮廓的边界框
                            largest_contour = max(contours, key=cv2.contourArea)
                            x, y, w, h = cv2.boundingRect(largest_contour)
                            
                            # 更新检测结果
                            det['x_center'] = (x + w / 2) / img_width
                            det['y_center'] = (y + h / 2) / img_height
                            det['width'] = w / img_width
                            det['height'] = h / img_height
                            det['bbox'] = [float(x), float(y), float(x + w), float(y + h)]
                            det['mask'] = mask
            
            return detections
            
        except Exception as e:
            print(f"SAM精细化失败: {e}")
            return detections
    
    def auto_annotate_directory(self, data_dir: str, output_dir: Optional[str] = None,
                               conf: float = 0.25, iou: float = 0.45,
                               classes: Optional[List[int]] = None) -> Dict[str, List[Dict]]:
        """
        自动标注目录下的所有图像
        
        Args:
            data_dir: 数据目录
            output_dir: 输出目录
            conf: 置信度阈值
            iou: IoU阈值
            classes: 指定检测的类别
        
        Returns:
            所有图像的标注结果
        """
        data_path = Path(data_dir)
        if not output_dir:
            output_dir = data_path.parent / f"{data_path.stem}_auto_annotate_labels"
        
        # 创建输出目录
        FileManager.create_directory(str(output_dir))
        
        # 获取所有图像文件
        image_files = FileManager.get_image_files(str(data_dir))
        
        results = {}
        for image_path in image_files:
            print(f"正在处理: {image_path}")
            annotations = self.auto_annotate_image(
                image_path, str(output_dir), conf, iou, classes
            )
            results[image_path] = annotations
        
        return results
    
    def create_dataset_yaml(self, data_dir: str, class_names: List[str], 
                           output_path: str = "dataset.yaml"):
        """创建数据集配置文件"""
        yaml_content = f"""# YOLO数据集配置文件
path: {os.path.abspath(data_dir)}  # 数据集根目录
train: images/train  # 训练图像目录 (相对于path)
val: images/val      # 验证图像目录 (相对于path)
test: images/test    # 测试图像目录 (可选)

# 类别
nc: {len(class_names)}  # 类别数量
names: {class_names}  # 类别名称
"""
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            print(f"数据集配置文件已保存: {output_path}")
        except Exception as e:
            print(f"保存数据集配置文件失败: {e}")
    
    def split_dataset(self, data_dir: str, train_ratio: float = 0.8, 
                     val_ratio: float = 0.1, test_ratio: float = 0.1):
        """分割数据集"""
        import random
        import shutil
        
        data_path = Path(data_dir)
        images_dir = data_path / "images"
        labels_dir = data_path / "labels"
        
        if not images_dir.exists() or not labels_dir.exists():
            print("错误: 找不到images或labels目录")
            return
        
        # 获取所有图像文件
        image_files = list(images_dir.glob("*"))
        image_files = [f for f in image_files if f.suffix.lower() in FileManager.SUPPORTED_IMAGE_FORMATS]
        
        # 随机打乱
        random.shuffle(image_files)
        
        # 计算分割点
        total = len(image_files)
        train_end = int(total * train_ratio)
        val_end = train_end + int(total * val_ratio)
        
        # 创建目录结构
        for split in ['train', 'val', 'test']:
            (data_path / "images" / split).mkdir(parents=True, exist_ok=True)
            (data_path / "labels" / split).mkdir(parents=True, exist_ok=True)
        
        # 分割文件
        splits = {
            'train': image_files[:train_end],
            'val': image_files[train_end:val_end],
            'test': image_files[val_end:]
        }
        
        for split_name, files in splits.items():
            for img_file in files:
                # 移动图像文件
                dst_img = data_path / "images" / split_name / img_file.name
                shutil.move(str(img_file), str(dst_img))
                
                # 移动对应的标签文件
                label_file = labels_dir / f"{img_file.stem}.txt"
                if label_file.exists():
                    dst_label = data_path / "labels" / split_name / label_file.name
                    shutil.move(str(label_file), str(dst_label))
        
        print(f"数据集分割完成: 训练集{len(splits['train'])}张, 验证集{len(splits['val'])}张, 测试集{len(splits['test'])}张")
