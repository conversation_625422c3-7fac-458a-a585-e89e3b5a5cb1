@echo off
echo YOLO Auto Annotation Tool Setup
echo ================================

echo Step 1: Creating virtual environment...
python -m venv yolo_env
if errorlevel 1 (
    echo Failed to create virtual environment
    pause
    exit /b 1
)

echo Step 2: Activating virtual environment...
call yolo_env\Scripts\activate.bat

echo Step 3: Upgrading pip...
python -m pip install --upgrade pip

echo Step 4: Installing dependencies...
pip install -r requirements.txt

echo Step 5: Running installation test...
python test_installation.py

echo Step 6: Starting YOLO Auto Annotation Tool...
python main.py

pause
