namespace eval ::tk {
    ::msgcat::mcset zh_cn "&Abort" "&中止"
    ::msgcat::mcset zh_cn "&About..." "&关于……"
    ::msgcat::mcset zh_cn "All Files" "所有文件"
    ::msgcat::mcset zh_cn "Application Error" "应用程序错误"
    ::msgcat::mcset zh_cn "&Apply" "&添加"
    ::msgcat::mcset zh_cn "Bold" "粗体"
    ::msgcat::mcset zh_cn "Bold Italic" "加粗斜体"
    ::msgcat::mcset zh_cn "&Blue" "&蓝色"
    ::msgcat::mcset zh_cn "Cancel" "取消"
    ::msgcat::mcset zh_cn "&Cancel" "&取消"
    ::msgcat::mcset zh_cn "Cannot change to the directory \"%1\$s\".\nPermission denied." "无法更改目录 \"%1\$s\"。\n访问被拒绝。"
    ::msgcat::mcset zh_cn "Choose Directory" "选择文件夹"
    ::msgcat::mcset zh_cn "Cl&ear" "清&除"
    ::msgcat::mcset zh_cn "&Clear Console" "&清除终端"
    ::msgcat::mcset zh_cn "Color" "颜色"
    ::msgcat::mcset zh_cn "Console" "终端"
    ::msgcat::mcset zh_cn "&Copy" "&复制"
    ::msgcat::mcset zh_cn "Cu&t" "剪&切"
    ::msgcat::mcset zh_cn "&Delete" "&删除"
    ::msgcat::mcset zh_cn "Details >>" "详细信息 >>"
    ::msgcat::mcset zh_cn "Directory \"%1\$s\" does not exist." "目录 \"%1\$s\" 不存在。"
    ::msgcat::mcset zh_cn "&Directory:" "&目录："
    ::msgcat::mcset zh_cn "&Edit" "&编辑"
    ::msgcat::mcset zh_cn "Effects" "效果"
    ::msgcat::mcset zh_cn "Error: %1\$s" "错误： %1\$s"
    ::msgcat::mcset zh_cn "E&xit" "退&出"
    ::msgcat::mcset zh_cn "&File" "&文件"
    ::msgcat::mcset zh_cn "File \"%1\$s\" already exists.\nDo you want to overwrite it?" "文件 \"%1\$s\" 已经存在。\n您想要覆盖它吗？"
    ::msgcat::mcset zh_cn "File \"%1\$s\" already exists.\n\n" "文件 \"%1\$s\" 已经存在。\n\n"
    ::msgcat::mcset zh_cn "File \"%1\$s\" does not exist." "文件 \"%1\$s\" 不存在。"
    ::msgcat::mcset zh_cn "File &name:" "文件&名："
    ::msgcat::mcset zh_cn "File &names:" "文件&名："
    ::msgcat::mcset zh_cn "Files of &type:" "文件&类型："
    ::msgcat::mcset zh_cn "Fi&les:" "文&件："
    ::msgcat::mcset zh_cn "&Filter" "&过滤"
    ::msgcat::mcset zh_cn "Fil&ter:" "过&滤："
    ::msgcat::mcset zh_cn "Font" "字体"
    ::msgcat::mcset zh_cn "&Font:" "&字体："
    ::msgcat::mcset zh_cn "Font st&yle:" "字体&样式："
    ::msgcat::mcset zh_cn "&Green" "&绿色"
    ::msgcat::mcset zh_cn "&Help" "&帮助"
    ::msgcat::mcset zh_cn "Hi" "你好"
    ::msgcat::mcset zh_cn "&Hide Console" "&隐藏终端"
    ::msgcat::mcset zh_cn "&Ignore" "&忽略"
    ::msgcat::mcset zh_cn "Invalid file name \"%1\$s\"." "无效的文件名 \"%1\$s\"。"
    ::msgcat::mcset zh_cn "Italic" "斜体"
    ::msgcat::mcset zh_cn "Log Files" "日志文件"
    ::msgcat::mcset zh_cn "&No" "&否"
    ::msgcat::mcset zh_cn "&OK" "&确定"
    ::msgcat::mcset zh_cn "OK" "确定"
    ::msgcat::mcset zh_cn "Ok" "确定"
    ::msgcat::mcset zh_cn "Open" "打开"
    ::msgcat::mcset zh_cn "&Open" "&打开"
    ::msgcat::mcset zh_cn "Open Multiple Files" "打开多个文件"
    ::msgcat::mcset zh_cn "P&aste" "粘&贴"
    ::msgcat::mcset zh_cn "&Quit" "&退出"
    ::msgcat::mcset zh_cn "&Red" "红色"
    ::msgcat::mcset zh_cn "Regular" "规则"
    ::msgcat::mcset zh_cn "Replace existing file?" "替换已有文件？"
    ::msgcat::mcset zh_cn "&Retry" "&重试"
    ::msgcat::mcset zh_cn "Sample" "样式"
    ::msgcat::mcset zh_cn "&Save" "&保存"
    ::msgcat::mcset zh_cn "Save As" "另存为"
    ::msgcat::mcset zh_cn "Save To Log" "保存到日志"
    ::msgcat::mcset zh_cn "Select Log File" "选择日志文件"
    ::msgcat::mcset zh_cn "Select a file to source" "选择一个源文件"
    ::msgcat::mcset zh_cn "&Selection:" "&选择："
    ::msgcat::mcset zh_cn "&Size:" "&大小："
    ::msgcat::mcset zh_cn "Show &Hidden Directories" "显示&隐藏目录"
    ::msgcat::mcset zh_cn "Show &Hidden Files and Directories" "显示&隐藏文件和目录"
    ::msgcat::mcset zh_cn "Skip Messages" "跳过信息"
    ::msgcat::mcset zh_cn "&Source..." "&来源……"
    ::msgcat::mcset zh_cn "Stri&keout" "删&除线"
    ::msgcat::mcset zh_cn "Tcl Scripts" "Tcl脚本"
    ::msgcat::mcset zh_cn "Tcl for Windows" "适用于Windows的Tcl"
    ::msgcat::mcset zh_cn "Text Files" "文本文档"
    ::msgcat::mcset zh_cn "&Underline" "&下划线"
    ::msgcat::mcset zh_cn "&Yes" "&确定"
    ::msgcat::mcset zh_cn "abort" "中止"
    ::msgcat::mcset zh_cn "blue" "蓝色"
    ::msgcat::mcset zh_cn "cancel" "取消"
    ::msgcat::mcset zh_cn "extension" "拓展"
    ::msgcat::mcset zh_cn "extensions" "拓展"
    ::msgcat::mcset zh_cn "green" "绿色"
    ::msgcat::mcset zh_cn "ignore" "忽略"
    ::msgcat::mcset zh_cn "ok" "确定"
    ::msgcat::mcset zh_cn "red" "红色"
    ::msgcat::mcset zh_cn "retry" "重试"
    ::msgcat::mcset zh_cn "yes" "确认"
}

