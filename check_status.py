#!/usr/bin/env python3
"""
检查YOLO自动标注工具状态
"""
import os
import sys

def check_models():
    """检查模型文件"""
    print("📁 模型文件检查")
    print("-" * 40)
    
    models = {
        "YOLO模型": ["yolo11n.pt", "yolo11s.pt", "yolo11m.pt", "yolo11l.pt", "yolo11x.pt"],
        "SAM模型": ["sam2.1_l.pt", "sam_l.pt", "sam_b.pt", "mobile_sam.pt"],
        "内置SAM": ["_internal/sampro/checkpoints/sam2.1_hiera_large.pt"]
    }
    
    for category, model_list in models.items():
        print(f"\n{category}:")
        found = False
        for model in model_list:
            if os.path.exists(model):
                size_mb = os.path.getsize(model) / (1024 * 1024)
                print(f"  ✅ {model} ({size_mb:.1f} MB)")
                found = True
        if not found:
            print(f"  ❌ 未找到{category}")

def check_functionality():
    """检查功能状态"""
    print("\n🔧 功能状态检查")
    print("-" * 40)
    
    try:
        from core.yolo_detector import YOLODetector
        detector = YOLODetector()
        if detector.model:
            print("✅ YOLO检测器: 正常")
        else:
            print("❌ YOLO检测器: 异常")
    except Exception as e:
        print(f"❌ YOLO检测器: 失败 ({e})")
    
    try:
        from core.auto_annotator import AutoAnnotator
        annotator = AutoAnnotator()
        if annotator.yolo_detector.model:
            print("✅ 自动标注器: YOLO功能正常")
        else:
            print("❌ 自动标注器: YOLO功能异常")
            
        if annotator.sam_model:
            print("✅ 自动标注器: SAM功能正常")
        else:
            print("⚠️ 自动标注器: SAM功能未启用")
    except Exception as e:
        print(f"❌ 自动标注器: 失败 ({e})")

def check_gui():
    """检查GUI组件"""
    print("\n🖥️ GUI组件检查")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5: 可用")
    except ImportError:
        print("❌ PyQt5: 不可用")
        return
    
    try:
        from gui.main_window import MainWindow
        print("✅ 主窗口: 可导入")
    except Exception as e:
        print(f"❌ 主窗口: 导入失败 ({e})")
    
    try:
        from gui.image_viewer import ImageViewer
        print("✅ 图像显示器: 可导入")
    except Exception as e:
        print(f"❌ 图像显示器: 导入失败 ({e})")
    
    try:
        from gui.label_manager import LabelManager
        print("✅ 标签管理器: 可导入")
    except Exception as e:
        print(f"❌ 标签管理器: 导入失败 ({e})")

def check_config():
    """检查配置"""
    print("\n⚙️ 配置检查")
    print("-" * 40)
    
    try:
        from utils.config import config
        print("✅ 配置系统: 正常")
        
        labels = config.get_labels()
        label_count = len(labels.get("label_to_id", {}))
        print(f"✅ 标签配置: {label_count} 个标签")
        
        model_path = config.get("model_path", "未设置")
        print(f"✅ 模型路径: {model_path}")
        
    except Exception as e:
        print(f"❌ 配置系统: 失败 ({e})")

def main():
    """主函数"""
    print("🔍 YOLO自动标注工具 - 状态检查")
    print("=" * 50)
    
    check_models()
    check_functionality()
    check_gui()
    check_config()
    
    print("\n" + "=" * 50)
    print("📊 状态总结")
    print("-" * 40)
    
    # 检查关键文件
    key_files = [
        "main.py",
        "sam2.1_l.pt",
        "yolo11n.pt"
    ]
    
    all_good = True
    for file in key_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            all_good = False
    
    if all_good:
        print("\n🎉 系统状态良好！")
        print("✅ 所有核心功能可用")
        print("✅ YOLO检测功能正常")
        print("✅ SAM精细分割已启用")
        print("✅ GUI界面完整")
        print("\n🚀 您可以:")
        print("  1. 使用完整的自动标注功能")
        print("  2. 享受YOLO+SAM的精确标注")
        print("  3. 进行高质量的数据标注工作")
    else:
        print("\n⚠️ 发现一些问题")
        print("但基础功能仍然可用")

if __name__ == "__main__":
    main()
