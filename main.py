#!/usr/bin/env python3
"""
YOLO自动标注工具主程序
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont, QIcon
from gui.main_window import MainWindow

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import PyQt5
    except ImportError:
        missing_packages.append("PyQt5")
    
    try:
        import cv2
    except ImportError:
        missing_packages.append("opencv-python")
    
    try:
        import numpy
    except ImportError:
        missing_packages.append("numpy")
    
    try:
        from PIL import Image
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import torch
    except ImportError:
        missing_packages.append("torch")
    
    try:
        from ultralytics import YOLO
    except ImportError:
        missing_packages.append("ultralytics")
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def create_splash_screen():
    """创建启动画面"""
    # 创建一个简单的启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # 添加文本
    splash.showMessage(
        "YOLO自动标注工具\n\n正在启动...",
        Qt.AlignCenter | Qt.AlignBottom,
        Qt.black
    )
    
    return splash

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("YOLO自动标注工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Tools")
    
    # 设置应用程序图标
    icon_path = project_root / "icons" / "logo.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建启动画面
    splash = create_splash_screen()
    splash.show()
    app.processEvents()
    
    try:
        # 创建主窗口
        splash.showMessage("正在初始化界面...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        main_window = MainWindow()
        
        splash.showMessage("正在加载模型...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        # 延迟显示主窗口
        def show_main_window():
            splash.finish(main_window)
            main_window.show()
        
        QTimer.singleShot(2000, show_main_window)  # 2秒后显示主窗口
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        splash.close()
        QMessageBox.critical(None, "启动错误", f"程序启动失败:\n{e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
