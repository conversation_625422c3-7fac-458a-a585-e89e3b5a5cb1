#!/usr/bin/env python3
"""
设置SAM模型脚本
将内置的SAM模型复制到正确的位置
"""
import os
import shutil
from pathlib import Path

def setup_sam_model():
    """设置SAM模型"""
    print("SAM模型设置工具")
    print("=" * 50)
    
    # 源文件路径
    source_model = "_internal/sampro/checkpoints/sam2.1_hiera_large.pt"
    
    # 目标文件路径（Ultralytics支持的名称）
    target_models = [
        "sam2.1_l.pt",
        "sam2_l.pt", 
        "sam_l.pt"
    ]
    
    if not os.path.exists(source_model):
        print(f"❌ 源模型文件不存在: {source_model}")
        return False
    
    source_size = os.path.getsize(source_model) / (1024 * 1024)
    print(f"✅ 找到源模型: {source_model} ({source_size:.1f} MB)")
    
    # 选择目标模型名称
    target_model = target_models[0]  # 使用sam2.1_l.pt
    
    if os.path.exists(target_model):
        print(f"✅ 目标模型已存在: {target_model}")
        return True
    
    try:
        print(f"正在复制模型文件...")
        print(f"从: {source_model}")
        print(f"到: {target_model}")
        
        shutil.copy2(source_model, target_model)
        
        # 验证复制结果
        if os.path.exists(target_model):
            target_size = os.path.getsize(target_model) / (1024 * 1024)
            print(f"✅ 复制成功: {target_model} ({target_size:.1f} MB)")
            return True
        else:
            print(f"❌ 复制失败: {target_model}")
            return False
            
    except Exception as e:
        print(f"❌ 复制过程中出错: {e}")
        return False

def test_sam_loading():
    """测试SAM模型加载"""
    print("\n测试SAM模型加载")
    print("=" * 50)
    
    try:
        from ultralytics import SAM
        
        # 测试加载
        model_path = "sam2.1_l.pt"
        if os.path.exists(model_path):
            print(f"正在测试加载: {model_path}")
            sam_model = SAM(model_path)
            print(f"✅ SAM模型加载成功!")
            return True
        else:
            print(f"❌ 模型文件不存在: {model_path}")
            return False
            
    except Exception as e:
        print(f"❌ SAM模型加载失败: {e}")
        return False

def test_auto_annotator():
    """测试自动标注器"""
    print("\n测试自动标注器")
    print("=" * 50)
    
    try:
        from core.auto_annotator import AutoAnnotator
        
        print("正在初始化自动标注器...")
        annotator = AutoAnnotator()
        
        if annotator.sam_model is not None:
            print("✅ 自动标注器初始化成功，SAM模型已加载!")
            print("🎉 精细分割功能已启用!")
            return True
        else:
            print("⚠️ 自动标注器初始化成功，但SAM模型未加载")
            return False
            
    except Exception as e:
        print(f"❌ 自动标注器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("YOLO自动标注工具 - SAM模型设置")
    print("=" * 60)
    
    # 设置SAM模型
    setup_success = setup_sam_model()
    
    if setup_success:
        # 测试加载
        load_success = test_sam_loading()
        
        if load_success:
            # 测试自动标注器
            annotator_success = test_auto_annotator()
            
            if annotator_success:
                print("\n" + "=" * 60)
                print("🎉 SAM模型设置完成!")
                print("✅ 精细分割功能已启用")
                print("✅ 自动标注器可以使用YOLO+SAM进行精细标注")
                print("\n现在您可以:")
                print("1. 重新启动YOLO自动标注工具")
                print("2. 使用'自动标注'功能获得更精确的结果")
                print("3. 享受YOLO+SAM的强大组合!")
                return 0
    
    print("\n" + "=" * 60)
    print("⚠️ SAM模型设置未完全成功")
    print("但YOLO检测功能仍然可用")
    print("\n您仍然可以:")
    print("1. 使用YOLO进行目标检测")
    print("2. 进行基础的自动标注")
    print("3. 正常使用其他所有功能")
    return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
